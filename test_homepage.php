<?php
/**
 * Homepage Testing and Validation Script
 * Comprehensive testing for the rebuilt homepage
 *
 * @version 1.0
 * <AUTHOR> Development Team
 * @description Complete testing script for all homepage components
 */

// Include configuration
require_once 'config/config.php';

// Test results array
$testResults = [];
$totalTests = 0;
$passedTests = 0;

/**
 * Test function helper
 */
function runTest($testName, $testFunction) {
    global $testResults, $totalTests, $passedTests;
    
    $totalTests++;
    try {
        $result = $testFunction();
        if ($result) {
            $testResults[] = ['name' => $testName, 'status' => 'PASS', 'message' => 'Test passed successfully'];
            $passedTests++;
        } else {
            $testResults[] = ['name' => $testName, 'status' => 'FAIL', 'message' => 'Test failed'];
        }
    } catch (Exception $e) {
        $testResults[] = ['name' => $testName, 'status' => 'ERROR', 'message' => $e->getMessage()];
    }
}

// Test 1: Check if main index.php file exists and is readable
runTest('Main Index File', function() {
    return file_exists('index.php') && is_readable('index.php');
});

// Test 2: Check if main CSS file exists and is readable
runTest('Main CSS File', function() {
    return file_exists('assets/css/homepage-new.css') && is_readable('assets/css/homepage-new.css');
});

// Test 3: Check if main JavaScript file exists and is readable
runTest('Main JavaScript File', function() {
    return file_exists('assets/js/homepage-interactions.js') && is_readable('assets/js/homepage-interactions.js');
});

// Test 4: Check all homepage component files
$componentFiles = [
    'includes/homepage_carousel.php',
    'includes/homepage_categories.php',
    'includes/homepage_featured_products.php',
    'includes/homepage_special_offers.php',
    'includes/homepage_about_store.php',
    'includes/homepage_customer_reviews.php',
    'includes/homepage_influencers.php',
    'includes/homepage_why_choose_us.php',
    'includes/homepage_newsletter.php',
    'includes/homepage_social_media.php'
];

foreach ($componentFiles as $file) {
    runTest("Component File: " . basename($file), function() use ($file) {
        return file_exists($file) && is_readable($file);
    });
}

// Test 5: Check database connection
runTest('Database Connection', function() {
    try {
        $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
        return true;
    } catch (PDOException $e) {
        return false;
    }
});

// Test 6: Check required database tables
$requiredTables = ['products', 'categories', 'reviews'];
foreach ($requiredTables as $table) {
    runTest("Database Table: $table", function() use ($table) {
        try {
            $result = fetchOne("SHOW TABLES LIKE '$table'");
            return !empty($result);
        } catch (Exception $e) {
            return false;
        }
    });
}

// Test 7: Check CSS file size and content
runTest('CSS File Content', function() {
    $cssContent = file_get_contents('assets/css/homepage-new.css');
    return strlen($cssContent) > 10000 && strpos($cssContent, ':root') !== false;
});

// Test 8: Check JavaScript file content
runTest('JavaScript File Content', function() {
    $jsContent = file_get_contents('assets/js/homepage-interactions.js');
    return strlen($jsContent) > 5000 && strpos($jsContent, 'DOMContentLoaded') !== false;
});

// Test 9: Check manifest.json file
runTest('Manifest File', function() {
    return file_exists('manifest.json') && is_readable('manifest.json');
});

// Test 10: Check header.php includes homepage CSS
runTest('Header Includes Homepage CSS', function() {
    $headerContent = file_get_contents('includes/header.php');
    return strpos($headerContent, 'homepage-new.css') !== false;
});

// Test 11: Validate HTML structure in index.php
runTest('HTML Structure Validation', function() {
    $indexContent = file_get_contents('index.php');
    return strpos($indexContent, '<main id="main-content"') !== false &&
           strpos($indexContent, '</main>') !== false &&
           strpos($indexContent, 'role="main"') !== false;
});

// Test 12: Check for accessibility features
runTest('Accessibility Features', function() {
    $headerContent = file_get_contents('includes/header.php');
    return strpos($headerContent, 'skip-to-main') !== false;
});

// Test 13: Check for responsive design meta tag
runTest('Responsive Design Meta Tag', function() {
    $headerContent = file_get_contents('includes/header.php');
    return strpos($headerContent, 'viewport-fit=cover') !== false;
});

// Test 14: Check for performance optimizations
runTest('Performance Optimizations', function() {
    $headerContent = file_get_contents('includes/header.php');
    return strpos($headerContent, 'preload') !== false &&
           strpos($headerContent, 'dns-prefetch') !== false;
});

// Test 15: Check component file syntax (Windows-compatible)
foreach ($componentFiles as $file) {
    runTest("PHP Syntax: " . basename($file), function() use ($file) {
        if (!file_exists($file)) {
            return false;
        }

        // Use token_get_all() for basic syntax checking
        $content = file_get_contents($file);
        $tokens = @token_get_all($content);

        if ($tokens === false || empty($tokens)) {
            return false;
        }

        // Check for PHP opening tag
        $hasOpenTag = false;
        foreach ($tokens as $token) {
            if (is_array($token) && $token[0] === T_OPEN_TAG) {
                $hasOpenTag = true;
                break;
            }
        }

        if (!$hasOpenTag) {
            return false;
        }

        // Try to parse the file for syntax errors
        ob_start();
        $errorReporting = error_reporting(E_PARSE);

        try {
            include $file;
            $result = true;
        } catch (ParseError $e) {
            $result = false;
        } catch (Error $e) {
            // Ignore other errors, we're only checking syntax
            $result = true;
        } catch (Exception $e) {
            // Ignore exceptions, we're only checking syntax
            $result = true;
        }

        error_reporting($errorReporting);
        ob_end_clean();

        return $result;
    });
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نتائج اختبار الصفحة الرئيسية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20px;
            background-color: #f8fafc;
            color: #1a202c;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5rem;
        }
        
        .header p {
            margin: 0;
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .summary {
            padding: 30px;
            background: #f7fafc;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .summary-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .summary-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .summary-label {
            color: #64748b;
            font-size: 0.9rem;
        }
        
        .pass { color: #16a34a; }
        .fail { color: #dc2626; }
        .error { color: #ea580c; }
        
        .results {
            padding: 30px;
        }
        
        .test-item {
            display: flex;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 8px;
            background: #f8fafc;
            border-left: 4px solid #e2e8f0;
        }
        
        .test-item.pass {
            background: #f0fdf4;
            border-left-color: #16a34a;
        }
        
        .test-item.fail {
            background: #fef2f2;
            border-left-color: #dc2626;
        }
        
        .test-item.error {
            background: #fff7ed;
            border-left-color: #ea580c;
        }
        
        .test-status {
            width: 80px;
            text-align: center;
            font-weight: bold;
            font-size: 0.9rem;
            margin-left: 15px;
        }
        
        .test-name {
            flex: 1;
            font-weight: 600;
            margin-left: 15px;
        }
        
        .test-message {
            color: #64748b;
            font-size: 0.9rem;
        }
        
        .footer {
            padding: 30px;
            background: #1a202c;
            color: white;
            text-align: center;
        }
        
        .success-rate {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #374151;
            border-radius: 4px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #16a34a 0%, #22c55e 100%);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 نتائج اختبار الصفحة الرئيسية</h1>
            <p>تقرير شامل لاختبار جميع مكونات الصفحة الرئيسية المُعاد بناؤها</p>
        </div>
        
        <div class="summary">
            <div class="summary-grid">
                <div class="summary-card">
                    <div class="summary-number"><?php echo $totalTests; ?></div>
                    <div class="summary-label">إجمالي الاختبارات</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number pass"><?php echo $passedTests; ?></div>
                    <div class="summary-label">اختبارات ناجحة</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number fail"><?php echo $totalTests - $passedTests; ?></div>
                    <div class="summary-label">اختبارات فاشلة</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number"><?php echo round(($passedTests / $totalTests) * 100, 1); ?>%</div>
                    <div class="summary-label">معدل النجاح</div>
                </div>
            </div>
        </div>
        
        <div class="results">
            <h2>تفاصيل الاختبارات</h2>
            <?php foreach ($testResults as $result): ?>
            <div class="test-item <?php echo strtolower($result['status']); ?>">
                <div class="test-status <?php echo strtolower($result['status']); ?>">
                    <?php 
                    echo $result['status'] === 'PASS' ? '✅ نجح' : 
                         ($result['status'] === 'FAIL' ? '❌ فشل' : '⚠️ خطأ'); 
                    ?>
                </div>
                <div class="test-name"><?php echo htmlspecialchars($result['name']); ?></div>
                <div class="test-message"><?php echo htmlspecialchars($result['message']); ?></div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <div class="footer">
            <div class="success-rate">
                معدل النجاح: <?php echo round(($passedTests / $totalTests) * 100, 1); ?>%
            </div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: <?php echo ($passedTests / $totalTests) * 100; ?>%"></div>
            </div>
            <p>
                <?php if ($passedTests === $totalTests): ?>
                🎉 ممتاز! جميع الاختبارات نجحت. الصفحة الرئيسية جاهزة للإنتاج.
                <?php elseif ($passedTests / $totalTests >= 0.9): ?>
                ✅ جيد جداً! معظم الاختبارات نجحت. راجع الاختبارات الفاشلة.
                <?php elseif ($passedTests / $totalTests >= 0.7): ?>
                ⚠️ مقبول. هناك بعض المشاكل التي تحتاج إلى إصلاح.
                <?php else: ?>
                ❌ يحتاج إلى عمل. هناك مشاكل كثيرة تحتاج إلى إصلاح.
                <?php endif; ?>
            </p>
            <p><small>تم إنشاء هذا التقرير في: <?php echo date('Y-m-d H:i:s'); ?></small></p>
        </div>
    </div>
</body>
</html>
