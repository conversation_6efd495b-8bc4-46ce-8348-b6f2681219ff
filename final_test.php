<?php
/**
 * Final Comprehensive Test - Windows Compatible
 * No external PHP commands required
 */

// Start output buffering to capture any errors
ob_start();
$errors = [];
$successes = [];

// Test 1: Check if files exist
$requiredFiles = [
    'config/functions.php',
    'includes/homepage_featured_products.php',
    'includes/homepage_special_offers.php',
    'index.php'
];

foreach ($requiredFiles as $file) {
    if (file_exists($file)) {
        $successes[] = "✅ File exists: $file";
    } else {
        $errors[] = "❌ File missing: $file";
    }
}

// Test 2: Include config and check function
try {
    require_once 'config/config.php';
    $successes[] = "✅ Config loaded successfully";
    
    if (function_exists('getProductImage')) {
        $successes[] = "✅ getProductImage() function is available";
    } else {
        $errors[] = "❌ getProductImage() function not found";
    }
} catch (Exception $e) {
    $errors[] = "❌ Config loading error: " . $e->getMessage();
}

// Test 3: Check function location
$functionsContent = file_get_contents('config/functions.php');
if (strpos($functionsContent, 'function getProductImage') !== false) {
    $successes[] = "✅ getProductImage() found in config/functions.php";
} else {
    $errors[] = "❌ getProductImage() not found in config/functions.php";
}

// Test 4: Check duplicates removed
$featuredContent = file_get_contents('includes/homepage_featured_products.php');
if (strpos($featuredContent, 'function getProductImage') === false) {
    $successes[] = "✅ Duplicate removed from homepage_featured_products.php";
} else {
    $errors[] = "❌ Duplicate still exists in homepage_featured_products.php";
}

$offersContent = file_get_contents('includes/homepage_special_offers.php');
if (strpos($offersContent, 'function getProductImage') === false) {
    $successes[] = "✅ Duplicate removed from homepage_special_offers.php";
} else {
    $errors[] = "❌ Duplicate still exists in homepage_special_offers.php";
}

// Test 5: Test function inclusion without conflicts
try {
    ob_start();
    include 'includes/homepage_featured_products.php';
    include 'includes/homepage_special_offers.php';
    ob_end_clean();
    $successes[] = "✅ Both files included without function conflicts";
} catch (Error $e) {
    $errors[] = "❌ Function conflict error: " . $e->getMessage();
} catch (Exception $e) {
    $errors[] = "❌ Include error: " . $e->getMessage();
}

// Test 6: Test function functionality
if (function_exists('getProductImage')) {
    try {
        $testProduct = ['image_url_1' => 'test.jpg'];
        $result = getProductImage($testProduct);
        if (!empty($result)) {
            $successes[] = "✅ getProductImage() function works correctly";
        } else {
            $errors[] = "❌ getProductImage() returned empty result";
        }
    } catch (Exception $e) {
        $errors[] = "❌ Function test error: " . $e->getMessage();
    }
}

// Test 7: Homepage loading test
try {
    ob_start();
    include 'index.php';
    $content = ob_get_contents();
    ob_end_clean();
    
    if (strlen($content) > 1000) {
        $successes[] = "✅ Homepage loads successfully (" . strlen($content) . " chars)";
    } else {
        $errors[] = "❌ Homepage content too short or empty";
    }
} catch (Error $e) {
    $errors[] = "❌ Homepage fatal error: " . $e->getMessage();
} catch (Exception $e) {
    $errors[] = "❌ Homepage error: " . $e->getMessage();
}

// Clear any buffered output
ob_end_clean();

// Display results
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Test Results</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #2c3e50, #34495e);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5rem;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            font-weight: 500;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border-left: 5px solid #28a745;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border-left: 5px solid #dc3545;
        }
        
        .summary {
            margin-top: 30px;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .summary.success {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
        
        .summary.error {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
        }
        
        .stats {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
        }
        
        .stat {
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Final Test Results</h1>
            <p>Function Conflict Fix Verification</p>
        </div>
        
        <div class="content">
            <h2>Test Results:</h2>
            
            <?php foreach ($successes as $success): ?>
                <div class="test-result success"><?php echo htmlspecialchars($success); ?></div>
            <?php endforeach; ?>
            
            <?php foreach ($errors as $error): ?>
                <div class="test-result error"><?php echo htmlspecialchars($error); ?></div>
            <?php endforeach; ?>
            
            <div class="summary <?php echo empty($errors) ? 'success' : 'error'; ?>">
                <div class="stats">
                    <div class="stat">
                        <div class="stat-number"><?php echo count($successes); ?></div>
                        <div class="stat-label">Passed</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number"><?php echo count($errors); ?></div>
                        <div class="stat-label">Failed</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number"><?php echo round((count($successes) / (count($successes) + count($errors))) * 100); ?>%</div>
                        <div class="stat-label">Success Rate</div>
                    </div>
                </div>
                
                <?php if (empty($errors)): ?>
                    <h3>🎉 ALL TESTS PASSED!</h3>
                    <p>The function conflict has been successfully resolved. The homepage is now working correctly without any PHP fatal errors.</p>
                    <p><strong>Status: ✅ PRODUCTION READY</strong></p>
                <?php else: ?>
                    <h3>❌ Some Tests Failed</h3>
                    <p>There are still some issues that need to be addressed. Please review the failed tests above.</p>
                    <p><strong>Status: ⚠️ NEEDS ATTENTION</strong></p>
                <?php endif; ?>
            </div>
            
            <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                <h3>Quick Links:</h3>
                <ul>
                    <li><a href="index.php" target="_blank">🏠 View Homepage</a></li>
                    <li><a href="simple_test.php" target="_blank">🔧 Simple Test</a></li>
                    <li><a href="verify_fix.php" target="_blank">✅ Detailed Verification</a></li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
