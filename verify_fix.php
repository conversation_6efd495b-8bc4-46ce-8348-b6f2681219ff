<?php
/**
 * Verification Script for Function Conflict Fix
 * Tests that the getProductImage() function conflict has been resolved
 */

echo "<h1>🔧 Function Conflict Fix Verification</h1>\n";
echo "<div style='font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5;'>\n";

// Test 1: Check if config/functions.php contains the function
echo "<h2>✅ Test 1: Function in config/functions.php</h2>\n";
if (file_exists('config/functions.php')) {
    $functionsContent = file_get_contents('config/functions.php');
    if (strpos($functionsContent, 'function getProductImage') !== false) {
        echo "<p style='color: green;'>✅ SUCCESS: getProductImage() function found in config/functions.php</p>\n";
    } else {
        echo "<p style='color: red;'>❌ ERROR: getProductImage() function NOT found in config/functions.php</p>\n";
    }
} else {
    echo "<p style='color: red;'>❌ ERROR: config/functions.php file not found</p>\n";
}

// Test 2: Check that homepage_featured_products.php doesn't contain the function
echo "<h2>✅ Test 2: Function removed from homepage_featured_products.php</h2>\n";
if (file_exists('includes/homepage_featured_products.php')) {
    $featuredContent = file_get_contents('includes/homepage_featured_products.php');
    if (strpos($featuredContent, 'function getProductImage') === false) {
        echo "<p style='color: green;'>✅ SUCCESS: getProductImage() function removed from homepage_featured_products.php</p>\n";
    } else {
        echo "<p style='color: red;'>❌ ERROR: getProductImage() function still exists in homepage_featured_products.php</p>\n";
    }
} else {
    echo "<p style='color: red;'>❌ ERROR: homepage_featured_products.php file not found</p>\n";
}

// Test 3: Check that homepage_special_offers.php doesn't contain the function
echo "<h2>✅ Test 3: Function removed from homepage_special_offers.php</h2>\n";
if (file_exists('includes/homepage_special_offers.php')) {
    $offersContent = file_get_contents('includes/homepage_special_offers.php');
    if (strpos($offersContent, 'function getProductImage') === false) {
        echo "<p style='color: green;'>✅ SUCCESS: getProductImage() function removed from homepage_special_offers.php</p>\n";
    } else {
        echo "<p style='color: red;'>❌ ERROR: getProductImage() function still exists in homepage_special_offers.php</p>\n";
    }
} else {
    echo "<p style='color: red;'>❌ ERROR: homepage_special_offers.php file not found</p>\n";
}

// Test 4: PHP Syntax validation (Windows-compatible)
echo "<h2>✅ Test 4: PHP Syntax Validation</h2>\n";
$filesToTest = [
    'includes/homepage_featured_products.php',
    'includes/homepage_special_offers.php',
    'config/functions.php'
];

$allPassed = true;
foreach ($filesToTest as $file) {
    if (file_exists($file)) {
        // Use token_get_all() to check for basic syntax errors
        $content = file_get_contents($file);
        $tokens = @token_get_all($content);

        if ($tokens !== false && !empty($tokens)) {
            // Check for common syntax issues
            $hasOpenTag = false;
            $syntaxError = false;
            $errorMessage = '';

            foreach ($tokens as $token) {
                if (is_array($token) && $token[0] === T_OPEN_TAG) {
                    $hasOpenTag = true;
                    break;
                }
            }

            if (!$hasOpenTag) {
                $syntaxError = true;
                $errorMessage = 'Missing PHP opening tag';
            }

            // Try to include the file to catch runtime syntax errors
            if (!$syntaxError) {
                ob_start();
                $errorReporting = error_reporting(E_PARSE);

                try {
                    include $file;
                    $syntaxError = false;
                } catch (ParseError $e) {
                    $syntaxError = true;
                    $errorMessage = $e->getMessage();
                } catch (Error $e) {
                    // Ignore other errors, we're only checking syntax
                    $syntaxError = false;
                } catch (Exception $e) {
                    // Ignore exceptions, we're only checking syntax
                    $syntaxError = false;
                }

                error_reporting($errorReporting);
                ob_end_clean();
            }

            if (!$syntaxError) {
                echo "<p style='color: green;'>✅ SUCCESS: $file - No syntax errors detected</p>\n";
            } else {
                echo "<p style='color: red;'>❌ ERROR: $file - Syntax errors found:</p>\n";
                echo "<pre style='background: #ffe6e6; padding: 10px; border-radius: 5px;'>$errorMessage</pre>\n";
                $allPassed = false;
            }
        } else {
            echo "<p style='color: red;'>❌ ERROR: $file - Could not parse file</p>\n";
            $allPassed = false;
        }
    } else {
        echo "<p style='color: red;'>❌ ERROR: $file - File not found</p>\n";
        $allPassed = false;
    }
}

// Test 5: Try to include both files to test for function conflicts
echo "<h2>✅ Test 5: Function Conflict Test</h2>\n";
try {
    // Include config/functions.php first
    require_once 'config/config.php';
    
    // Buffer output to prevent any HTML from the included files
    ob_start();
    
    // Try to include both homepage files
    include 'includes/homepage_featured_products.php';
    include 'includes/homepage_special_offers.php';
    
    // Clear the buffer
    ob_end_clean();
    
    echo "<p style='color: green;'>✅ SUCCESS: Both files included without function conflicts</p>\n";
    
    // Test if the function is accessible
    if (function_exists('getProductImage')) {
        echo "<p style='color: green;'>✅ SUCCESS: getProductImage() function is accessible</p>\n";
        
        // Test the function with sample data
        $testProduct = [
            'image_url_1' => 'https://example.com/test-image.jpg',
            'image' => 'test-image.jpg'
        ];
        
        $imageUrl = getProductImage($testProduct);
        if (!empty($imageUrl)) {
            echo "<p style='color: green;'>✅ SUCCESS: getProductImage() function returns: $imageUrl</p>\n";
        } else {
            echo "<p style='color: orange;'>⚠️ WARNING: getProductImage() function returned empty result</p>\n";
        }
    } else {
        echo "<p style='color: red;'>❌ ERROR: getProductImage() function is not accessible</p>\n";
        $allPassed = false;
    }
    
} catch (Error $e) {
    echo "<p style='color: red;'>❌ ERROR: Function conflict detected - " . $e->getMessage() . "</p>\n";
    $allPassed = false;
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ ERROR: Exception occurred - " . $e->getMessage() . "</p>\n";
    $allPassed = false;
}

// Final result
echo "<h2>🎯 Final Result</h2>\n";
if ($allPassed) {
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb;'>\n";
    echo "<h3>🎉 ALL TESTS PASSED!</h3>\n";
    echo "<p>The function conflict has been successfully resolved:</p>\n";
    echo "<ul>\n";
    echo "<li>✅ getProductImage() function moved to config/functions.php</li>\n";
    echo "<li>✅ Duplicate functions removed from homepage components</li>\n";
    echo "<li>✅ No PHP syntax errors detected</li>\n";
    echo "<li>✅ No function conflicts when including files</li>\n";
    echo "<li>✅ Function is accessible and working correctly</li>\n";
    echo "</ul>\n";
    echo "<p><strong>The homepage should now load without any PHP fatal errors!</strong></p>\n";
    echo "</div>\n";
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; border: 1px solid #f5c6cb;'>\n";
    echo "<h3>❌ SOME TESTS FAILED</h3>\n";
    echo "<p>Please review the errors above and fix them before proceeding.</p>\n";
    echo "</div>\n";
}

echo "</div>\n";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f8f9fa;
}

h1 {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 30px;
}

h2 {
    color: #34495e;
    border-bottom: 2px solid #3498db;
    padding-bottom: 5px;
}

pre {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
}

ul {
    line-height: 1.6;
}

li {
    margin-bottom: 5px;
}
</style>
